# 🎬 PLAN EXPORTABLE - REFACTORING CINÉMATOGRAPHIQUE COMPLET

**📅 Créé le:** 15/08/2025  
**🎯 Objectif:** Transformer le système complexe en expérience cinématographique automatique  
**⏱️ Durée estimée:** 6-7 conversations  
**📋 Status:** Phase 1 terminée ✅

---

## 🚀 RÉSUMÉ EXÉCUTIF

**AVANT (Complexe):**
- 8 phases de cycle
- 3 composants de contrôle  
- 1 contexte global complexe
- Multiple boutons et curseurs
- Navigation manuelle entre phases

**APRÈS (Simple):**
- 4 phases automatiques (aube/midi/coucher/nuit)
- 1 bouton circulaire central
- 0 contexte complexe
- Animation automatique complète
- Expérience cinématographique immersive

---

## 🌅☀️🌇🌙 POSITIONS ASTRONOMIQUES EXACTES (CISCO CRITICAL)

### PHASE 1: AUBE (2 minutes)
- **SOLEIL:** -40° → -10° (invisible → apparition)
- **LUNE:** Opacity 0.3 → 0.0 (disparition progressive)
- **ÉTOILES:** Density "high" → "medium" (estompage)
- **NUAGES:** `brightness(0.7) + hue-rotate(8deg)` (rosés)

### PHASE 2: MIDI (2 minutes)  
- **SOLEIL:** -10° → 90° (ZÉNITH ABSOLU)
- **LUNE:** Opacity 0.0 (INVISIBLE complet)
- **ÉTOILES:** Density "none" (INVISIBLES jour)
- **NUAGES:** `brightness(1.2)` (blancs éclatants)

### PHASE 3: COUCHER (2 minutes)
- **SOLEIL:** 90° → 170° (descente horizon opposé)
- **LUNE:** Opacity 0.0 → 0.3 (APPARITION progressive)
- **ÉTOILES:** Density "none" → "medium" (scintillement)
- **NUAGES:** `brightness(0.9) + hue-rotate(25deg)` (dorés)

### PHASE 4: NUIT (2 minutes)
- **SOLEIL:** 170° → 200° (INVISIBLE complet)
- **LUNE:** Opacity 0.3 → 1.0 (VISIBLE complet)
- **ÉTOILES:** Density "medium" → "high" (densité max)
- **NUAGES:** `brightness(0.4)` (sombres/invisibles)

**🎯 RÈGLE CRITIQUE:** Soleil et Lune JAMAIS visibles simultanément

---

## 📋 PHASES DÉTAILLÉES

### ✅ PHASE 1: CRÉATION SYSTÈME CINEMA (TERMINÉE)

**Fichiers créés:**
- `Components/Cinema/CinemaController.tsx` - Contrôleur principal
- `Components/Cinema/CinemaTransition.tsx` - Volets cinématographiques  
- `Components/Cinema/AutoCycleManager.tsx` - Cycle automatique 4 phases
- `Components/Cinema/CinemaTest.tsx` - Interface de test

**Fonctionnalités:**
- Bouton circulaire central (astuce anti-navigateur)
- Volets glissants horizontaux (effet cinéma)
- Timer automatique 8 minutes (2min/phase)
- Positions astronomiques exactes intégrées

---

### 🎯 PHASE 2: INTÉGRATION DANS APP.TSX

**Objectifs:**
- Remplacer `<DayCycleProvider>` par `<CinemaController>`
- Supprimer imports obsolètes (DayCycleContext, etc.)
- Tester bouton circulaire central
- Vérifier déclenchement audio (astuce navigateur)

**Fichiers à modifier:**
- `App.tsx` (lignes 2277-2290 environ)

**Code à remplacer:**
```typescript
// ANCIEN
<DayCycleProvider initialCycleDuration={8} ...>
  <DynamicBackground>

// NOUVEAU  
<CinemaController onPhaseChange={handlePhaseChange}>
  <DynamicBackground skyMode={currentPhase}>
```

---

### 🎨 PHASE 3: ADAPTATION DYNAMICBACKGROUND

**Objectifs:**
- Simplifier de 8 phases → 4 modes simples
- Conserver dégradés cohérents (règles d'or CISCO)
- Supprimer logique complexe DayCycleContext

**Fichiers à modifier:**
- `Components/Background/DynamicBackground.tsx`

**Modifications:**
```typescript
// Simplifier les modes
type SimpleMode = 'aube' | 'midi' | 'coucher' | 'nuit';

// Conserver dégradés principaux
const BACKGROUND_MODES = {
  aube: { primary: '#FF6B6B', secondary: '#4ECDC4', tertiary: '#45B7D1' },
  midi: { primary: '#87CEEB', secondary: '#98D8E8', tertiary: '#B8E6B8' },
  coucher: { primary: '#FF8C42', secondary: '#FF6B35', tertiary: '#F7931E' },
  nuit: { primary: '#2C3E50', secondary: '#34495E', tertiary: '#1A252F' }
};
```

---

### 🌟 PHASE 4: MODIFICATION ASTRONOMICALLAYER

**Objectifs:**
- Supprimer dépendance `useDayCycleOptional()`
- Utiliser props directes `skyMode`
- Synchroniser avec AutoCycleManager

**Fichiers à modifier:**
- `Components/Background/AstronomicalLayer.tsx`

**Modifications:**
```typescript
// ANCIEN
const dayCycleContext = useDayCycleOptional();
const currentPhase = dayCycleContext?.currentPhase || skyMode;

// NOUVEAU
interface AstronomicalLayerProps {
  skyMode: 'aube' | 'midi' | 'coucher' | 'nuit';
}
```

---

### 🎵 PHASE 5: ADAPTATION AMBIENTSOUNDMANAGERV2

**Objectifs:**
- Supprimer logique contexte complexe
- Utiliser props `skyMode` directes
- Synchronisation audio parfaite avec phases

**Fichiers à modifier:**
- `Components/Audio/AmbientSoundManagerV2.tsx`

**Configuration sons:**
```typescript
const SOUND_CONFIG = {
  aube: { sounds: ['village_morning_birds_roosters.mp3'], folder: 'aube' },
  midi: { sounds: ['campagne-birds.mp3', 'forest_cicada.mp3'], folder: 'midi' },
  coucher: { sounds: ['bird-chirp.mp3', 'grillon-drome.mp3'], folder: 'coucher-soleil' },
  nuit: { sounds: ['hibou-molkom.mp3', 'night-atmosphere-with-crickets-374652.mp3'], folder: 'nuit-profonde' }
};
```

---

### 🧹 PHASE 6: NETTOYAGE RADICAL ANCIEN CODE

**Fichiers à SUPPRIMER:**
- `Components/Context/DayCycleContext.tsx`
- `Components/DayCycleController.tsx`  
- `Components/Hooks/useDayCycleTimer.tsx`

**Références à nettoyer:**
- Tous les imports `useDayCycle`, `DayCycleProvider`
- Props `onPhaseChange`, `onSoundChange` obsolètes
- Variables `currentPhase` liées au contexte

**⚠️ ATTENTION:** Vérifier AUCUNE référence restante (conflits)

---

### 🧪 PHASE 7: TESTS ET VALIDATION FINALE

**Tests fonctionnels:**
- Bouton circulaire déclenche expérience ✓
- Volets s'ouvrent/ferment correctement ✓
- Cycle 4 phases respecté (2min chacune) ✓
- Audio synchronisé avec phases ✓
- Positions astronomiques exactes ✓

**Tests visuels:**
- Dégradés cohérents selon règles CISCO ✓
- Nuages éclairés correctement par phase ✓
- Soleil/Lune jamais simultanés ✓
- Étoiles inversement proportionnelles au soleil ✓

---

## 🎯 INSTRUCTIONS POUR NOUVELLE CONVERSATION

**1. Importer ce plan:**
```
"Voici le plan exportable pour continuer le refactoring cinématographique.
Nous sommes à la PHASE 2: Intégration dans App.tsx"
```

**2. Vérifier fichier Cisco.md:**
- Consulter avant/pendant/après chaque phase
- Respecter règles d'or (dégradés/nuages/paysage cohérents)
- Nettoyage radical obligatoire

**3. Positions astronomiques:**
- TOUJOURS respecter les angles exacts
- Soleil/Lune jamais simultanés
- Transitions progressives et logiques

---

## 📊 MÉTRIQUES DE SUCCÈS

- **Simplicité:** 1 bouton vs multiples contrôles ✓
- **Performance:** Moins de composants, plus fluide ✓  
- **UX:** Expérience immersive automatique ✓
- **Maintenance:** Code plus simple et lisible ✓
- **Audio:** Contournement restrictions navigateur ✓

---

**🚀 PRÊT POUR LA PHASE 2 !**
