import React, { useState, useRef } from 'react';
import CinemaTransition, { CinemaTransitionRef } from './CinemaTransition';
import WelcomeButton from './WelcomeButton';

// 🎬 CISCO: Interface pour le contrôleur cinématographique
interface CinemaControllerProps {
  children: React.ReactNode;
  onExperienceStart?: () => void;
  onExperienceComplete?: () => void;
}

// 🎬 CISCO: États du système cinématographique
type CinemaState = 'idle' | 'starting' | 'opening' | 'running' | 'complete';

// 🎬 CISCO: Contrôleur principal de l'expérience cinématographique
const CinemaController: React.FC<CinemaControllerProps> = ({
  children,
  onExperienceStart,
  // onExperienceComplete
}) => {
  const [cinemaState, setCinemaState] = useState<CinemaState>('idle');
  const cinemaTransitionRef = useRef<CinemaTransitionRef>(null);

  // 🎬 CISCO: Démarrage de l'expérience complète
  const startExperience = async () => {
    // console.log('🎬 CISCO: Démarrage expérience cinématographique complète');
    
    try {
      // Phase 1: Préparation
      setCinemaState('starting');
      
      // Notifier le parent du démarrage
      if (onExperienceStart) {
        onExperienceStart();
      }

      // Petit délai pour l'effet visuel
      await new Promise(resolve => setTimeout(resolve, 500));

      // Phase 2: Ouverture des volets (12 secondes ultra-progressive)
      setCinemaState('opening');
      // console.log('🎬 CISCO: Ouverture des volets ultra-progressive...');
      
      if (cinemaTransitionRef.current) {
        await cinemaTransitionRef.current.openCurtains();
      }

      // Phase 3: Expérience en cours
      setCinemaState('running');
      // console.log('🎬 CISCO: Expérience en cours - Volets ouverts');

      // L'expérience se termine automatiquement après la durée du ModeLeverSoleil
      // ou peut être contrôlée par le parent

    } catch (error) {
      console.error('🎬 CISCO: Erreur lors du démarrage:', error);
      setCinemaState('idle');
    }
  };

  // 🎬 CISCO: Fin de l'expérience
  // const completeExperience = async () => {
  //   console.log('🎬 CISCO: Fin de l\'expérience');
    
  //   setCinemaState('complete');
    
  //   if (onExperienceComplete) {
  //     onExperienceComplete();
  //   }

  //   // Optionnel: Fermer les volets après un délai
  //   setTimeout(async () => {
  //     if (cinemaTransitionRef.current) {
  //       await cinemaTransitionRef.current.closeCurtains();
  //       setCinemaState('idle');
  //     }
  //   }, 3000);
  // };

  // 🎬 CISCO: Reset du système
  // const resetExperience = () => {
  //   console.log('🎬 CISCO: Reset expérience');
  //   setCinemaState('idle');
  //   if (cinemaTransitionRef.current) {
  //     cinemaTransitionRef.current.resetCurtains();
  //   }
  // };

  return (
    <>
      {/* 🎬 CISCO: Système de volets cinématographiques */}
      <CinemaTransition ref={cinemaTransitionRef}>
        {children}
      </CinemaTransition>

      {/* 🎬 CISCO: Bouton de bienvenue élégant */}
      <WelcomeButton
        onStartExperience={startExperience}
        isVisible={cinemaState === 'idle'}
      />

      {/* 🎬 CISCO: Interface propre et professionnelle - Debug supprimé */}
    </>
  );
};

export default CinemaController;
